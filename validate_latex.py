#!/usr/bin/env python3
"""
Simple LaTeX validation script to check for common syntax errors
"""

import re
import sys

def validate_latex_file(filename):
    """Validate a LaTeX file for common syntax errors"""
    errors = []
    warnings = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    # Check for matching \begin{} and \end{} environments
    begin_pattern = r'\\begin\{([^}]+)\}'
    end_pattern = r'\\end\{([^}]+)\}'
    
    env_stack = []
    
    for i, line in enumerate(lines, 1):
        # Find \begin{} commands
        for match in re.finditer(begin_pattern, line):
            env_name = match.group(1)
            env_stack.append((env_name, i))
        
        # Find \end{} commands
        for match in re.finditer(end_pattern, line):
            env_name = match.group(1)
            if not env_stack:
                errors.append(f"Line {i}: \\end{{{env_name}}} without matching \\begin")
            else:
                last_env, last_line = env_stack.pop()
                if last_env != env_name:
                    errors.append(f"Line {i}: \\end{{{env_name}}} doesn't match \\begin{{{last_env}}} from line {last_line}")
    
    # Check for unmatched \begin{} commands
    for env_name, line_num in env_stack:
        errors.append(f"Line {line_num}: \\begin{{{env_name}}} without matching \\end")
    
    # Check for unmatched braces
    brace_count = 0
    for i, line in enumerate(lines, 1):
        for char in line:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count < 0:
                    errors.append(f"Line {i}: Extra closing brace")
                    brace_count = 0
    
    if brace_count > 0:
        warnings.append(f"Document has {brace_count} unmatched opening braces")
    
    # Check for \documentclass and \begin{document}
    if not re.search(r'\\documentclass', content):
        errors.append("Missing \\documentclass command")
    
    if not re.search(r'\\begin\{document\}', content):
        errors.append("Missing \\begin{document}")
    
    if not re.search(r'\\end\{document\}', content):
        errors.append("Missing \\end{document}")
    
    # Check for common LaTeX errors
    if re.search(r'\\&', content):
        warnings.append("Found \\& - make sure this is intentional (use & for table alignment)")
    
    # Check for unescaped special characters in text
    special_chars = ['%', '#', '$', '&']
    for i, line in enumerate(lines, 1):
        # Skip lines that are comments or in verbatim environments
        if line.strip().startswith('%') or '\\begin{lstlisting}' in line or '\\begin{verbatim}' in line:
            continue
        
        for char in special_chars:
            if char in line and f'\\{char}' not in line:
                # Check if it's in a command or environment where it's allowed
                if not (char == '$' and ('\\$' in line or line.count('$') % 2 == 0)):
                    warnings.append(f"Line {i}: Unescaped '{char}' character")
    
    return errors, warnings

def main():
    if len(sys.argv) != 2:
        print("Usage: python validate_latex.py <latex_file>")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        errors, warnings = validate_latex_file(filename)
        
        print(f"LaTeX Validation Results for {filename}")
        print("=" * 50)
        
        if errors:
            print(f"\n❌ ERRORS ({len(errors)}):")
            for error in errors:
                print(f"  • {error}")
        
        if warnings:
            print(f"\n⚠️  WARNINGS ({len(warnings)}):")
            for warning in warnings:
                print(f"  • {warning}")
        
        if not errors and not warnings:
            print("\n✅ No errors or warnings found!")
        
        if errors:
            print(f"\n❌ Validation failed with {len(errors)} errors")
            sys.exit(1)
        else:
            print(f"\n✅ Validation passed with {len(warnings)} warnings")
            sys.exit(0)
            
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
